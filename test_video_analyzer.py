#!/usr/bin/env python3
"""
اختبار محلل الفيديو
Video Analyzer Test

يقوم بفحص محلل الفيديو والتأكد من عمله بدون أخطاء TensorFlow
"""

import sys
import os
import logging

# إعداد البيئة أولاً
os.environ['TF_CPP_MIN_LOG_LEVEL'] = '3'
os.environ['TF_ENABLE_ONEDNN_OPTS'] = '0'
os.environ['PROTOCOL_BUFFERS_PYTHON_IMPLEMENTATION'] = 'python'
os.environ['CUDA_VISIBLE_DEVICES'] = '-1'

# تعطيل التحذيرات
import warnings
warnings.filterwarnings('ignore')

# إضافة مجلد src إلى مسار Python
sys.path.insert(0, str(os.path.join(os.path.dirname(__file__), "src")))

logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')
logger = logging.getLogger(__name__)

def test_tensorflow_detector():
    """اختبار TensorFlow Detector"""
    print("🔍 اختبار TensorFlow Detector...")
    
    try:
        from ai.tensorflow_detector import TensorFlowDetector
        
        detector = TensorFlowDetector()
        if detector.is_available():
            print("✅ TensorFlow Detector متوفر ويعمل")
            return True
        else:
            print("⚠️ TensorFlow Detector غير متوفر")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في TensorFlow Detector: {e}")
        return False

def test_hybrid_analyzer():
    """اختبار النظام الهجين المتقدم"""
    print("\n🔍 اختبار النظام الهجين المتقدم...")
    
    try:
        from ai.hybrid_advanced_analyzer import HybridAdvancedAnalyzer
        
        analyzer = HybridAdvancedAnalyzer()
        available_analyzers = len(analyzer.available_analyzers)
        
        print(f"✅ النظام الهجين متوفر مع {available_analyzers} محلل")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في النظام الهجين: {e}")
        return False

def test_enhanced_content_analyzer():
    """اختبار محلل المحتوى المحسن"""
    print("\n🔍 اختبار محلل المحتوى المحسن...")
    
    try:
        from ai.enhanced_content_analyzer import EnhancedContentAnalyzer
        
        analyzer = EnhancedContentAnalyzer()
        available_features = sum(analyzer.available_features.values())
        total_features = len(analyzer.available_features)
        
        print(f"✅ محلل المحتوى المحسن متوفر ({available_features}/{total_features} ميزة)")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في محلل المحتوى المحسن: {e}")
        return False

def test_universal_analyzer():
    """اختبار المحلل الشامل"""
    print("\n🔍 اختبار المحلل الشامل...")
    
    try:
        from ai.universal_video_analyzer import UniversalVideoAnalyzer
        
        analyzer = UniversalVideoAnalyzer()
        print("✅ المحلل الشامل متوفر ويعمل")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في المحلل الشامل: {e}")
        return False

def test_livestream_analyzer():
    """اختبار محلل البثوث المباشرة"""
    print("\n🔍 اختبار محلل البثوث المباشرة...")
    
    try:
        from ai.livestream_analyzer import LivestreamAnalyzer
        
        analyzer = LivestreamAnalyzer()
        print("✅ محلل البثوث المباشرة متوفر ويعمل")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في محلل البثوث المباشرة: {e}")
        return False

def test_gui_integration():
    """اختبار تكامل واجهة المستخدم"""
    print("\n🔍 اختبار تكامل واجهة المستخدم...")
    
    try:
        # اختبار استيراد النوافذ الرئيسية
        from gui.main_window import VideoEditorApp
        print("✅ النافذة الرئيسية متوفرة")
        
        from gui.universal_analyzer_window import UniversalAnalyzerWindow
        print("✅ نافذة المحلل الشامل متوفرة")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في تكامل واجهة المستخدم: {e}")
        return False

def main():
    """الدالة الرئيسية للاختبار"""
    
    print("🚀 اختبار محلل الفيديو الشامل")
    print("=" * 50)
    
    # إحصائيات الاختبار
    total_tests = 0
    passed_tests = 0
    
    # اختبار TensorFlow Detector
    total_tests += 1
    if test_tensorflow_detector():
        passed_tests += 1
    
    # اختبار النظام الهجين
    total_tests += 1
    if test_hybrid_analyzer():
        passed_tests += 1
    
    # اختبار محلل المحتوى المحسن
    total_tests += 1
    if test_enhanced_content_analyzer():
        passed_tests += 1
    
    # اختبار المحلل الشامل
    total_tests += 1
    if test_universal_analyzer():
        passed_tests += 1
    
    # اختبار محلل البثوث المباشرة
    total_tests += 1
    if test_livestream_analyzer():
        passed_tests += 1
    
    # اختبار تكامل واجهة المستخدم
    total_tests += 1
    if test_gui_integration():
        passed_tests += 1
    
    # النتائج النهائية
    print("\n" + "=" * 50)
    print("📊 ملخص نتائج اختبار محلل الفيديو:")
    print(f"🔧 الاختبارات: {passed_tests}/{total_tests} نجحت")
    
    # تقييم الحالة العامة
    success_rate = (passed_tests / total_tests) * 100
    print(f"📈 معدل النجاح: {success_rate:.1f}%")
    
    # التوصيات
    print(f"\n💡 التقييم:")
    
    if success_rate >= 90:
        print("🎉 ممتاز! جميع مكونات محلل الفيديو تعمل بشكل مثالي")
    elif success_rate >= 75:
        print("✅ جيد! معظم مكونات محلل الفيديو تعمل بشكل صحيح")
    elif success_rate >= 50:
        print("⚠️ متوسط! بعض مكونات محلل الفيديو تحتاج إصلاح")
    else:
        print("❌ ضعيف! محلل الفيديو يحتاج إصلاح شامل")
    
    if success_rate >= 75:
        print("\n🎬 يمكنك الآن:")
        print("   • تشغيل التطبيق: python main.py")
        print("   • استخدام محلل البثوث المباشرة")
        print("   • تحليل الفيديوهات بالذكاء الاصطناعي")
        print("   • إنشاء مقاطع شورتس احترافية")
    else:
        print("\n🔧 للإصلاح:")
        print("   • شغل: python fix_tensorflow_issue.py")
        print("   • أو: python quick_tensorflow_fix.py")
    
    return success_rate >= 75

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف الاختبار")
        sys.exit(0)
    except Exception as e:
        print(f"\n💥 خطأ غير متوقع: {e}")
        sys.exit(1)
