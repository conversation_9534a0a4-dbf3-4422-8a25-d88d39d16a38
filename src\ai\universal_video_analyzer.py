"""
محلل الفيديو الشامل - يدعم جميع أنواع المقاطع
Universal Video Analyzer - Supports all types of videos
"""

import logging
import os
from pathlib import Path
from typing import List, Dict, Any, Optional
import time
import cv2

from ai.livestream_analyzer import LivestreamAnalyzer
try:
    from ai.enhanced_content_analyzer import EnhancedContentAnalyzer
    ENHANCED_ANALYZER_AVAILABLE = True
except ImportError:
    ENHANCED_ANALYZER_AVAILABLE = False
    logger.warning("⚠️ Enhanced Content Analyzer غير متوفر")

# استيراد المحلل الآمن كبديل
try:
    from ai.safe_content_analyzer import SafeContentAnalyzer
    SAFE_ANALYZER_AVAILABLE = True
except ImportError:
    SAFE_ANALYZER_AVAILABLE = False
    logger.warning("⚠️ Safe Content Analyzer غير متوفر")
from ai.advanced_highlight_detector import AdvancedHighlightDetector
from core.video_processor import VideoProcessor
from utils.file_utils import FileManager

logger = logging.getLogger(__name__)

class UniversalVideoAnalyzer:
    """محلل فيديو شامل يدعم البثوث المباشرة والمقاطع العادية"""
    
    def __init__(self):
        """تهيئة المحلل الشامل"""
        self.livestream_analyzer = LivestreamAnalyzer()

        # تهيئة المحلل المحسن مع معالجة الأخطاء
        self.enhanced_analyzer = None
        if ENHANCED_ANALYZER_AVAILABLE:
            try:
                self.enhanced_analyzer = EnhancedContentAnalyzer()
                logger.info("✅ تم تهيئة المحلل المحسن")
            except Exception as e:
                logger.warning(f"⚠️ تحذير في تهيئة المحلل المحسن: {e}")
                self.enhanced_analyzer = None

        # استخدام المحلل الآمن كبديل
        if self.enhanced_analyzer is None and SAFE_ANALYZER_AVAILABLE:
            try:
                self.enhanced_analyzer = SafeContentAnalyzer()
                logger.info("✅ تم تهيئة المحلل الآمن كبديل")
            except Exception as e:
                logger.warning(f"⚠️ تحذير في تهيئة المحلل الآمن: {e}")
                self.enhanced_analyzer = None

        self.highlight_detector = AdvancedHighlightDetector()
        self.video_processor = VideoProcessor()
        self.file_manager = FileManager()

        logger.info("تم تهيئة المحلل الشامل للفيديو")
    
    def analyze_video(self, video_path: str, video_type: str = "auto", 
                     target_clips: int = None) -> List[Dict[str, Any]]:
        """تحليل أي نوع من الفيديوهات
        
        Args:
            video_path: مسار ملف الفيديو
            video_type: نوع الفيديو ("livestream", "regular", "auto")
            target_clips: عدد المقاطع المطلوبة
            
        Returns:
            قائمة بمقاطع الشورتس المُنشأة
        """
        try:
            logger.info(f"بدء تحليل الفيديو: {video_path}")
            
            # التحقق من وجود الملف
            if not os.path.exists(video_path):
                logger.error(f"ملف الفيديو غير موجود: {video_path}")
                return []
            
            # الحصول على معلومات الفيديو
            video_info = self.video_processor.get_video_info(video_path)
            if not video_info:
                logger.error("لا يمكن الحصول على معلومات الفيديو")
                return []
            
            duration = video_info['duration']
            logger.info(f"مدة الفيديو: {duration/60:.1f} دقيقة")
            
            # تحديد نوع الفيديو تلقائياً إذا لم يتم تحديده
            if video_type == "auto":
                video_type = self._detect_video_type(video_path, video_info)
                logger.info(f"تم تحديد نوع الفيديو تلقائياً: {video_type}")
            
            # تحليل الفيديو حسب النوع
            if video_type == "livestream":
                return self._analyze_as_livestream(video_path, target_clips)
            elif video_type == "regular":
                return self._analyze_as_regular_video(video_path, target_clips)
            else:
                logger.warning(f"نوع فيديو غير معروف: {video_type}")
                return self._analyze_as_regular_video(video_path, target_clips)
                
        except Exception as e:
            logger.error(f"خطأ في تحليل الفيديو: {e}")
            return []
    
    def _detect_video_type(self, video_path: str, video_info: Dict[str, Any]) -> str:
        """تحديد نوع الفيديو تلقائياً"""
        try:
            duration = video_info['duration']
            
            # إذا كان الفيديو طويل جداً (أكثر من ساعة)، فهو على الأرجح بث مباشر
            if duration > 3600:
                return "livestream"
            
            # إذا كان الفيديو قصير (أقل من 10 دقائق)، فهو مقطع عادي
            if duration < 600:
                return "regular"
            
            # للفيديوهات المتوسطة، نحتاج تحليل أكثر تفصيلاً
            # فحص اسم الملف للكلمات المفتاحية
            filename = Path(video_path).name.lower()
            livestream_keywords = ['live', 'stream', 'broadcast', 'بث', 'مباشر']
            
            for keyword in livestream_keywords:
                if keyword in filename:
                    return "livestream"
            
            # فحص جودة الفيديو - البثوث المباشرة عادة بجودة أقل
            # (هذا تقدير تقريبي)
            if 'width' in video_info and 'height' in video_info:
                resolution = video_info['width'] * video_info['height']
                if resolution < 1280 * 720:  # أقل من HD
                    return "livestream"
            
            # افتراضياً، اعتبره مقطع عادي
            return "regular"
            
        except Exception as e:
            logger.error(f"خطأ في تحديد نوع الفيديو: {e}")
            return "regular"
    
    def _analyze_as_livestream(self, video_path: str, target_clips: int = None) -> List[Dict[str, Any]]:
        """تحليل الفيديو كبث مباشر"""
        try:
            logger.info("تحليل الفيديو كبث مباشر")
            
            # استخدام محلل البثوث المباشرة
            if hasattr(self.livestream_analyzer, 'analyze_coherent_livestream'):
                # استخدام التحليل المترابط إذا كان متاحاً
                return self.livestream_analyzer.analyze_coherent_livestream(video_path, target_clips)
            else:
                # استخدام التحليل العادي للبثوث
                return self.livestream_analyzer.analyze_long_livestream(video_path, target_clips)
                
        except Exception as e:
            logger.error(f"خطأ في تحليل البث المباشر: {e}")
            # العودة للتحليل العادي في حالة الخطأ
            return self._analyze_as_regular_video(video_path, target_clips)
    
    def _analyze_as_regular_video(self, video_path: str, target_clips: int = None) -> List[Dict[str, Any]]:
        """تحليل الفيديو كمقطع عادي"""
        try:
            logger.info("تحليل الفيديو كمقطع عادي")
            
            # الحصول على معلومات الفيديو
            video_info = self.video_processor.get_video_info(video_path)
            duration = video_info['duration']
            
            # تحديد عدد المقاطع تلقائياً إذا لم يتم تحديده
            if target_clips is None:
                if duration < 300:  # أقل من 5 دقائق
                    target_clips = 1
                elif duration < 900:  # أقل من 15 دقيقة
                    target_clips = 2
                elif duration < 1800:  # أقل من 30 دقيقة
                    target_clips = 3
                else:
                    target_clips = min(5, int(duration / 600))  # مقطع كل 10 دقائق
            
            logger.info(f"عدد المقاطع المستهدف: {target_clips}")
            
            # استخدام كاشف اللقطات المتقدم
            highlights = self.highlight_detector.detect_highlights(video_path, target_clips)
            
            if not highlights:
                logger.warning("لم يتم العثور على لقطات بارزة، استخدام التحليل الأساسي")
                return self._basic_video_analysis(video_path, target_clips)
            
            # تحويل اللقطات إلى مقاطع شورتس
            clips = []
            for i, highlight in enumerate(highlights):
                try:
                    clip_data = self._create_clip_from_highlight(video_path, highlight, i + 1)
                    if clip_data:
                        clips.append(clip_data)
                except Exception as e:
                    logger.error(f"خطأ في إنشاء المقطع {i + 1}: {e}")
                    continue
            
            logger.info(f"تم إنشاء {len(clips)} مقطع من الفيديو العادي")
            return clips
            
        except Exception as e:
            logger.error(f"خطأ في تحليل الفيديو العادي: {e}")
            return self._basic_video_analysis(video_path, target_clips)
    
    def _basic_video_analysis(self, video_path: str, target_clips: int = 3) -> List[Dict[str, Any]]:
        """تحليل أساسي للفيديو كحل احتياطي"""
        try:
            logger.info("استخدام التحليل الأساسي للفيديو")
            
            video_info = self.video_processor.get_video_info(video_path)
            duration = video_info['duration']
            
            # تقسيم الفيديو إلى أجزاء متساوية
            clips = []
            clip_duration = 30  # 30 ثانية لكل مقطع
            segment_duration = duration / target_clips
            
            for i in range(target_clips):
                start_time = i * segment_duration + (segment_duration - clip_duration) / 2
                start_time = max(0, start_time)
                end_time = min(start_time + clip_duration, duration)
                
                if end_time - start_time < 10:  # تجنب المقاطع القصيرة جداً
                    continue
                
                # إنشاء المقطع
                output_file = self.file_manager.create_output_file(
                    f"basic_clip_{i+1}", "mp4"
                )
                
                success = self.video_processor.trim_video(
                    video_path, str(output_file), start_time, end_time
                )
                
                if success:
                    clip_data = {
                        'file_path': str(output_file),
                        'start_time': start_time,
                        'end_time': end_time,
                        'duration': end_time - start_time,
                        'type': 'basic_segment',
                        'confidence': 0.5,
                        'title': f"مقطع {i + 1}",
                        'description': f"مقطع أساسي من الدقيقة {start_time/60:.1f}",
                        'hashtags': ['#shorts', '#video', '#clip']
                    }
                    clips.append(clip_data)
                    logger.info(f"تم إنشاء مقطع أساسي {i + 1}")
            
            return clips
            
        except Exception as e:
            logger.error(f"خطأ في التحليل الأساسي: {e}")
            return []
    
    def _create_clip_from_highlight(self, video_path: str, highlight, clip_number: int) -> Optional[Dict[str, Any]]:
        """إنشاء مقطع شورتس من لقطة بارزة"""
        try:
            # تحديد أوقات البداية والنهاية
            start_time = highlight.start_time
            end_time = highlight.end_time
            
            # التأكد من أن المقطع ليس طويل جداً (حد أقصى 60 ثانية)
            if end_time - start_time > 60:
                end_time = start_time + 60
            
            # التأكد من أن المقطع ليس قصير جداً (حد أدنى 10 ثوان)
            if end_time - start_time < 10:
                end_time = start_time + 10
            
            # إنشاء ملف الإخراج
            output_file = self.file_manager.create_output_file(
                f"highlight_clip_{clip_number}", "mp4"
            )
            
            # قص الفيديو
            success = self.video_processor.trim_video(
                video_path, str(output_file), start_time, end_time
            )
            
            if success:
                clip_data = {
                    'file_path': str(output_file),
                    'start_time': start_time,
                    'end_time': end_time,
                    'duration': end_time - start_time,
                    'type': highlight.highlight_type.value if hasattr(highlight, 'highlight_type') else 'highlight',
                    'confidence': highlight.confidence if hasattr(highlight, 'confidence') else 0.7,
                    'title': f"لقطة بارزة {clip_number}",
                    'description': highlight.description if hasattr(highlight, 'description') else f"لقطة بارزة من الدقيقة {start_time/60:.1f}",
                    'hashtags': self._generate_hashtags_for_highlight(highlight)
                }
                return clip_data
            else:
                logger.error(f"فشل في قص المقطع {clip_number}")
                return None
                
        except Exception as e:
            logger.error(f"خطأ في إنشاء المقطع من اللقطة: {e}")
            return None
    
    def _generate_hashtags_for_highlight(self, highlight) -> List[str]:
        """إنشاء هاشتاجات للقطة البارزة"""
        hashtags = ['#shorts', '#viral', '#trending']
        
        try:
            if hasattr(highlight, 'highlight_type'):
                if highlight.highlight_type.value == 'exciting':
                    hashtags.extend(['#exciting', '#amazing', '#wow'])
                elif highlight.highlight_type.value == 'funny':
                    hashtags.extend(['#funny', '#comedy', '#lol'])
                elif highlight.highlight_type.value == 'shocking':
                    hashtags.extend(['#shocking', '#surprise', '#omg'])
            
            # إضافة هاشتاجات عامة
            hashtags.extend(['#video', '#content', '#entertainment'])
            
        except Exception as e:
            logger.error(f"خطأ في إنشاء الهاشتاجات: {e}")
        
        return hashtags[:10]  # حد أقصى 10 هاشتاجات
    
    def get_supported_formats(self) -> List[str]:
        """الحصول على قائمة التنسيقات المدعومة"""
        return [
            '.mp4', '.avi', '.mov', '.mkv', '.flv', '.wmv', 
            '.webm', '.m4v', '.3gp', '.ts', '.mts'
        ]
    
    def is_supported_format(self, file_path: str) -> bool:
        """التحقق من دعم تنسيق الملف"""
        file_extension = Path(file_path).suffix.lower()
        return file_extension in self.get_supported_formats()

# دالة مساعدة للاستخدام السريع
def analyze_any_video(video_path: str, video_type: str = "auto", 
                     target_clips: int = None) -> List[Dict[str, Any]]:
    """دالة مساعدة لتحليل أي نوع من الفيديوهات"""
    analyzer = UniversalVideoAnalyzer()
    return analyzer.analyze_video(video_path, video_type, target_clips)
