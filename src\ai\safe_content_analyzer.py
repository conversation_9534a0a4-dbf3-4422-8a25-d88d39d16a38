"""
محلل المحتوى الآمن - نسخة بديلة بدون مشاكل TensorFlow
Safe Content Analyzer - Alternative version without TensorFlow issues

نسخة مبسطة من محلل المحتوى تتجنب مشاكل التوافق مع TensorFlow
"""

import cv2
import numpy as np
import logging
from typing import List, Dict, Any, Tuple, Optional
from dataclasses import dataclass
from pathlib import Path
import time

logger = logging.getLogger(__name__)

# محاولة استيراد المكتبات الأساسية فقط
try:
    import mediapipe as mp
    MEDIAPIPE_AVAILABLE = True
    logger.info("✅ MediaPipe متوفر")
except ImportError:
    MEDIAPIPE_AVAILABLE = False
    logger.warning("⚠️ MediaPipe غير متوفر")

try:
    import librosa
    LIBROSA_AVAILABLE = True
    logger.info("✅ Librosa متوفر")
except ImportError:
    LIBROSA_AVAILABLE = False
    logger.warning("⚠️ Librosa غير متوفر")

# محاولة استيراد النظام الهجين بحذر
try:
    from ai.yolo_detector import YOLODetector
    YOLO_AVAILABLE = True
    logger.info("✅ YOLO متوفر")
except ImportError:
    YOLO_AVAILABLE = False
    logger.warning("⚠️ YOLO غير متوفر")

class SafeContentAnalyzer:
    """محلل المحتوى الآمن - يتجنب مشاكل TensorFlow"""
    
    def __init__(self):
        """تهيئة محلل المحتوى الآمن"""
        self.available_features = {
            'mediapipe': MEDIAPIPE_AVAILABLE,
            'librosa': LIBROSA_AVAILABLE,
            'yolo': YOLO_AVAILABLE,
            'opencv': True  # دائماً متوفر
        }
        
        # تهيئة YOLO إذا كان متوفراً
        self.yolo_detector = None
        if YOLO_AVAILABLE:
            try:
                self.yolo_detector = YOLODetector()
                if not self.yolo_detector.is_available():
                    self.yolo_detector = None
                    self.available_features['yolo'] = False
            except Exception as e:
                logger.warning(f"تحذير في تهيئة YOLO: {e}")
                self.yolo_detector = None
                self.available_features['yolo'] = False
        
        # تهيئة MediaPipe إذا كان متوفراً
        if MEDIAPIPE_AVAILABLE:
            try:
                self.mp_face_detection = mp.solutions.face_detection.FaceDetection(
                    model_selection=1, min_detection_confidence=0.5
                )
                self.mp_pose = mp.solutions.pose.Pose(
                    static_image_mode=False,
                    model_complexity=1,
                    enable_segmentation=False,
                    min_detection_confidence=0.5
                )
            except Exception as e:
                logger.warning(f"تحذير في تهيئة MediaPipe: {e}")
                self.available_features['mediapipe'] = False
        
        logger.info(f"تم تهيئة محلل المحتوى الآمن - الميزات المتاحة: {sum(self.available_features.values())}/4")
    
    def analyze_frame_basic(self, frame: np.ndarray) -> Dict[str, Any]:
        """تحليل أساسي للإطار باستخدام OpenCV فقط"""
        try:
            # تحليل أساسي بـ OpenCV
            gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
            
            # كشف الوجوه الأساسي
            face_cascade = cv2.CascadeClassifier(cv2.data.haarcascades + 'haarcascade_frontalface_default.xml')
            faces = face_cascade.detectMultiScale(gray, 1.1, 4)
            
            # كشف الحركة الأساسي
            edges = cv2.Canny(gray, 50, 150)
            motion_score = np.sum(edges) / (frame.shape[0] * frame.shape[1])
            
            # حساب الوضوح
            laplacian_var = cv2.Laplacian(gray, cv2.CV_64F).var()
            
            return {
                'face_count': len(faces),
                'motion_score': float(motion_score),
                'sharpness': float(laplacian_var),
                'frame_quality': min(float(laplacian_var) / 100.0, 1.0) * 100,
                'analysis_method': 'opencv_basic'
            }
            
        except Exception as e:
            logger.error(f"خطأ في التحليل الأساسي: {e}")
            return {
                'face_count': 0,
                'motion_score': 0.0,
                'sharpness': 0.0,
                'frame_quality': 0.0,
                'analysis_method': 'error'
            }
    
    def analyze_frame_advanced(self, frame: np.ndarray) -> Dict[str, Any]:
        """تحليل متقدم للإطار باستخدام YOLO إذا كان متوفراً"""
        try:
            # البدء بالتحليل الأساسي
            result = self.analyze_frame_basic(frame)
            
            # إضافة تحليل YOLO إذا كان متوفراً
            if self.yolo_detector and self.available_features['yolo']:
                try:
                    yolo_analysis = self.yolo_detector.detect_frame(frame)
                    result.update({
                        'person_count': yolo_analysis.person_count,
                        'object_count': yolo_analysis.object_count,
                        'activity_score': yolo_analysis.activity_score,
                        'interesting_objects': yolo_analysis.interesting_objects,
                        'analysis_method': 'yolo_enhanced'
                    })
                except Exception as e:
                    logger.warning(f"تحذير في تحليل YOLO: {e}")
            
            # إضافة تحليل MediaPipe إذا كان متوفراً
            if MEDIAPIPE_AVAILABLE and self.available_features['mediapipe']:
                try:
                    rgb_frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
                    
                    # كشف الوجوه المتقدم
                    face_results = self.mp_face_detection.process(rgb_frame)
                    if face_results.detections:
                        result['advanced_face_count'] = len(face_results.detections)
                        result['analysis_method'] = 'mediapipe_enhanced'
                    
                    # كشف الوضعيات
                    pose_results = self.mp_pose.process(rgb_frame)
                    if pose_results.pose_landmarks:
                        result['pose_detected'] = True
                        result['analysis_method'] = 'mediapipe_pose'
                    
                except Exception as e:
                    logger.warning(f"تحذير في تحليل MediaPipe: {e}")
            
            return result
            
        except Exception as e:
            logger.error(f"خطأ في التحليل المتقدم: {e}")
            return self.analyze_frame_basic(frame)
    
    def analyze_video_segment(self, video_path: str, start_time: float = 0.0, 
                            end_time: float = None, sample_rate: int = 30) -> List[Dict[str, Any]]:
        """تحليل مقطع فيديو"""
        try:
            cap = cv2.VideoCapture(video_path)
            fps = cap.get(cv2.CAP_PROP_FPS)
            total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
            
            if end_time is None:
                end_time = total_frames / fps
            
            start_frame = int(start_time * fps)
            end_frame = int(end_time * fps)
            
            cap.set(cv2.CAP_PROP_POS_FRAMES, start_frame)
            
            analyses = []
            frame_number = start_frame
            
            while frame_number <= end_frame:
                ret, frame = cap.read()
                if not ret:
                    break
                
                # أخذ عينة حسب المعدل المحدد
                if (frame_number - start_frame) % sample_rate == 0:
                    timestamp = frame_number / fps
                    
                    # اختيار نوع التحليل حسب الميزات المتاحة
                    if self.available_features['yolo'] or self.available_features['mediapipe']:
                        analysis = self.analyze_frame_advanced(frame)
                    else:
                        analysis = self.analyze_frame_basic(frame)
                    
                    analysis['timestamp'] = timestamp
                    analysis['frame_number'] = frame_number
                    analyses.append(analysis)
                
                frame_number += 1
            
            cap.release()
            logger.info(f"تم تحليل {len(analyses)} إطار من المقطع")
            return analyses
            
        except Exception as e:
            logger.error(f"خطأ في تحليل مقطع الفيديو: {e}")
            return []
    
    def find_best_moments(self, analyses: List[Dict[str, Any]], 
                         min_quality: float = 30.0) -> List[Dict[str, Any]]:
        """العثور على أفضل اللحظات"""
        try:
            best_moments = []
            
            for analysis in analyses:
                # حساب نقاط الجودة الإجمالية
                quality_score = 0.0
                
                # نقاط للوجوه
                face_count = analysis.get('face_count', 0) + analysis.get('advanced_face_count', 0)
                quality_score += face_count * 20
                
                # نقاط للأشخاص (YOLO)
                person_count = analysis.get('person_count', 0)
                quality_score += person_count * 25
                
                # نقاط للنشاط
                activity_score = analysis.get('activity_score', 0)
                quality_score += activity_score * 0.3
                
                # نقاط للحركة
                motion_score = analysis.get('motion_score', 0)
                quality_score += motion_score * 100
                
                # نقاط للوضوح
                sharpness = analysis.get('sharpness', 0)
                quality_score += min(sharpness / 100.0, 1.0) * 20
                
                if quality_score >= min_quality:
                    moment = {
                        'timestamp': analysis.get('timestamp', 0),
                        'quality_score': quality_score,
                        'analysis_method': analysis.get('analysis_method', 'unknown'),
                        'details': analysis
                    }
                    best_moments.append(moment)
            
            # ترتيب حسب الجودة
            best_moments.sort(key=lambda x: x['quality_score'], reverse=True)
            
            logger.info(f"تم العثور على {len(best_moments)} لحظة جيدة")
            return best_moments
            
        except Exception as e:
            logger.error(f"خطأ في العثور على أفضل اللحظات: {e}")
            return []
    
    def get_available_features(self) -> Dict[str, bool]:
        """الحصول على الميزات المتاحة"""
        return self.available_features.copy()
    
    def is_advanced_analysis_available(self) -> bool:
        """التحقق من توفر التحليل المتقدم"""
        return self.available_features['yolo'] or self.available_features['mediapipe']
