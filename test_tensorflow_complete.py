#!/usr/bin/env python3
"""
اختبار شامل لـ TensorFlow والمكونات المرتبطة
Complete TensorFlow and Related Components Test

يقوم بفحص شامل لجميع مكونات TensorFlow والتأكد من عملها
"""

import sys
import os
import logging
import warnings

# إعداد البيئة أولاً
os.environ['TF_CPP_MIN_LOG_LEVEL'] = '3'
os.environ['TF_ENABLE_ONEDNN_OPTS'] = '0'
os.environ['PROTOCOL_BUFFERS_PYTHON_IMPLEMENTATION'] = 'python'
os.environ['CUDA_VISIBLE_DEVICES'] = '-1'

# تعطيل التحذيرات
warnings.filterwarnings('ignore')

logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')
logger = logging.getLogger(__name__)

def test_tensorflow_basic():
    """اختبار TensorFlow الأساسي"""
    print("🔍 اختبار TensorFlow الأساسي...")
    
    try:
        import tensorflow as tf
        print(f"✅ TensorFlow مثبت - الإصدار: {tf.__version__}")
        
        # اختبار العمليات الأساسية
        a = tf.constant([1, 2, 3])
        b = tf.constant([4, 5, 6])
        c = tf.add(a, b)
        
        print(f"✅ العمليات الأساسية تعمل: {c.numpy()}")
        return True
        
    except ImportError:
        print("❌ TensorFlow غير مثبت")
        return False
    except Exception as e:
        print(f"❌ خطأ في TensorFlow: {e}")
        return False

def test_keras():
    """اختبار Keras"""
    print("\n🔍 اختبار Keras...")
    
    try:
        import tensorflow as tf
        
        # إنشاء نموذج بسيط
        model = tf.keras.Sequential([
            tf.keras.layers.Dense(10, activation='relu', input_shape=(5,)),
            tf.keras.layers.Dense(1, activation='sigmoid')
        ])
        
        model.compile(optimizer='adam', loss='binary_crossentropy')
        print("✅ Keras يعمل بشكل صحيح")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في Keras: {e}")
        return False

def test_protobuf():
    """اختبار Protobuf"""
    print("\n🔍 اختبار Protobuf...")
    
    try:
        import google.protobuf
        print(f"✅ Protobuf مثبت - الإصدار: {google.protobuf.__version__}")
        return True
        
    except ImportError:
        print("❌ Protobuf غير مثبت")
        return False
    except Exception as e:
        print(f"❌ خطأ في Protobuf: {e}")
        return False

def test_google_ai():
    """اختبار Google AI"""
    print("\n🔍 اختبار Google AI...")
    
    try:
        import google.generativeai as genai
        print("✅ Google Generative AI متوفر")
        
        import google.ai.generativelanguage as glm
        print("✅ Google AI Generative Language متوفر")
        return True
        
    except ImportError as e:
        print(f"❌ Google AI غير متوفر: {e}")
        return False
    except Exception as e:
        print(f"❌ خطأ في Google AI: {e}")
        return False

def test_video_analysis_components():
    """اختبار مكونات تحليل الفيديو"""
    print("\n🔍 اختبار مكونات تحليل الفيديو...")
    
    components = {
        'opencv-python': 'cv2',
        'numpy': 'numpy',
        'PIL': 'PIL',
        'moviepy': 'moviepy.editor',
        'ultralytics': 'ultralytics'
    }
    
    working_components = []
    failed_components = []
    
    for name, module in components.items():
        try:
            __import__(module)
            print(f"✅ {name} متوفر")
            working_components.append(name)
        except ImportError:
            print(f"❌ {name} غير متوفر")
            failed_components.append(name)
        except Exception as e:
            print(f"⚠️ {name} مشكلة: {e}")
            failed_components.append(name)
    
    return len(working_components), len(failed_components)

def test_advanced_ai_components():
    """اختبار المكونات المتقدمة للذكاء الاصطناعي"""
    print("\n🔍 اختبار المكونات المتقدمة للذكاء الاصطناعي...")
    
    advanced_components = {
        'mediapipe': 'mediapipe',
        'deepface': 'deepface',
        'transformers': 'transformers',
        'torch': 'torch',
        'librosa': 'librosa'
    }
    
    working_advanced = []
    failed_advanced = []
    
    for name, module in advanced_components.items():
        try:
            __import__(module)
            print(f"✅ {name} متوفر")
            working_advanced.append(name)
        except ImportError:
            print(f"❌ {name} غير متوفر")
            failed_advanced.append(name)
        except Exception as e:
            print(f"⚠️ {name} مشكلة: {e}")
            failed_advanced.append(name)
    
    return len(working_advanced), len(failed_advanced)

def test_tensorflow_object_detection():
    """اختبار TensorFlow Object Detection"""
    print("\n🔍 اختبار TensorFlow Object Detection...")
    
    try:
        # محاولة استيراد TensorFlow Object Detection API
        import tensorflow as tf
        
        # اختبار تحميل نموذج مدرب مسبقاً
        print("✅ TensorFlow Object Detection API متوفر")
        return True
        
    except Exception as e:
        print(f"⚠️ TensorFlow Object Detection API غير متوفر: {e}")
        return False

def main():
    """الدالة الرئيسية للاختبار"""
    
    print("🚀 اختبار شامل لـ TensorFlow والمكونات المرتبطة")
    print("=" * 60)
    
    # إحصائيات الاختبار
    total_tests = 0
    passed_tests = 0
    
    # اختبار TensorFlow الأساسي
    total_tests += 1
    if test_tensorflow_basic():
        passed_tests += 1
    
    # اختبار Keras
    total_tests += 1
    if test_keras():
        passed_tests += 1
    
    # اختبار Protobuf
    total_tests += 1
    if test_protobuf():
        passed_tests += 1
    
    # اختبار Google AI
    total_tests += 1
    if test_google_ai():
        passed_tests += 1
    
    # اختبار مكونات تحليل الفيديو
    working_video, failed_video = test_video_analysis_components()
    total_video_components = working_video + failed_video
    
    # اختبار المكونات المتقدمة
    working_ai, failed_ai = test_advanced_ai_components()
    total_ai_components = working_ai + failed_ai
    
    # اختبار TensorFlow Object Detection
    total_tests += 1
    if test_tensorflow_object_detection():
        passed_tests += 1
    
    # النتائج النهائية
    print("\n" + "=" * 60)
    print("📊 ملخص نتائج الاختبار:")
    print(f"🔧 الاختبارات الأساسية: {passed_tests}/{total_tests} نجحت")
    print(f"🎬 مكونات تحليل الفيديو: {working_video}/{total_video_components} تعمل")
    print(f"🤖 المكونات المتقدمة للذكاء الاصطناعي: {working_ai}/{total_ai_components} تعمل")
    
    # تقييم الحالة العامة
    basic_success_rate = (passed_tests / total_tests) * 100
    video_success_rate = (working_video / total_video_components) * 100 if total_video_components > 0 else 0
    ai_success_rate = (working_ai / total_ai_components) * 100 if total_ai_components > 0 else 0
    
    print(f"\n📈 معدلات النجاح:")
    print(f"   🔧 الأساسية: {basic_success_rate:.1f}%")
    print(f"   🎬 تحليل الفيديو: {video_success_rate:.1f}%")
    print(f"   🤖 الذكاء الاصطناعي: {ai_success_rate:.1f}%")
    
    # التوصيات
    print(f"\n💡 التوصيات:")
    
    if basic_success_rate >= 75:
        print("✅ النظام الأساسي يعمل بشكل جيد")
    else:
        print("⚠️ يحتاج النظام الأساسي إلى إصلاح")
    
    if video_success_rate >= 60:
        print("✅ مكونات تحليل الفيديو كافية للعمل")
    else:
        print("⚠️ يُنصح بتثبيت المزيد من مكونات تحليل الفيديو")
    
    if ai_success_rate >= 40:
        print("✅ المكونات المتقدمة متوفرة جزئياً")
    else:
        print("💡 يمكن تثبيت المكونات المتقدمة لميزات إضافية")
    
    print(f"\n🚀 لتشغيل التطبيق:")
    print("python main.py")
    
    return basic_success_rate >= 75

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف الاختبار")
        sys.exit(0)
    except Exception as e:
        print(f"\n💥 خطأ غير متوقع: {e}")
        sys.exit(1)
