#!/usr/bin/env python3
"""
إصلاح مشكلة TensorFlow
TensorFlow Issue Fix

يقوم بإصلاح مشكلة توافق TensorFlow مع tf-keras
"""

import sys
import subprocess
import logging

logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')
logger = logging.getLogger(__name__)

def fix_tensorflow_issue():
    """إصلاح مشكلة TensorFlow"""

    print("🔧 إصلاح مشكلة TensorFlow...")
    print("=" * 50)

    try:
        # الخطوة 1: إعداد البيئة أولاً
        print("1️⃣ إعداد متغيرات البيئة...")
        import os
        os.environ['TF_CPP_MIN_LOG_LEVEL'] = '3'
        os.environ['TF_ENABLE_ONEDNN_OPTS'] = '0'
        os.environ['PROTOCOL_BUFFERS_PYTHON_IMPLEMENTATION'] = 'python'
        print("✅ تم إعداد متغيرات البيئة")

        # الخطوة 2: فحص TensorFlow الحالي
        print("\n2️⃣ فحص TensorFlow الحالي...")
        try:
            import tensorflow as tf
            current_version = tf.__version__
            print(f"✅ TensorFlow موجود - الإصدار: {current_version}")

            # اختبار بسيط
            test_tensor = tf.constant([1, 2, 3])
            print(f"✅ TensorFlow يعمل بشكل صحيح")
            return True

        except ImportError:
            print("⚠️ TensorFlow غير مثبت")
        except Exception as e:
            print(f"⚠️ مشكلة في TensorFlow: {e}")

        # الخطوة 3: تثبيت/إصلاح TensorFlow
        print("\n3️⃣ تثبيت TensorFlow...")

        # تثبيت أحدث إصدار مستقر
        result = subprocess.run([
            sys.executable, "-m", "pip", "install", "--upgrade", "tensorflow"
        ], capture_output=True, text=True)

        if result.returncode == 0:
            print("✅ تم تثبيت TensorFlow بنجاح")
        else:
            print(f"❌ فشل في تثبيت TensorFlow: {result.stderr}")
            return False

        # الخطوة 4: إصلاح مشاكل التوافق
        print("\n4️⃣ إصلاح مشاكل التوافق...")

        # تحديث المكتبات المرتبطة
        compatibility_packages = [
            "protobuf",
            "google-generativeai",
            "google-ai-generativelanguage",
            "opentelemetry-proto",
            "opentelemetry-exporter-otlp-proto-common",
            "opentelemetry-exporter-otlp-proto-grpc"
        ]

        for package in compatibility_packages:
            print(f"   تحديث {package}...")
            result = subprocess.run([
                sys.executable, "-m", "pip", "install", "--upgrade", package
            ], capture_output=True, text=True)

            if result.returncode == 0:
                print(f"   ✅ تم تحديث {package}")
            else:
                print(f"   ⚠️ تحذير في تحديث {package}")

        # الخطوة 5: اختبار نهائي
        print("\n5️⃣ اختبار TensorFlow النهائي...")
        try:
            import tensorflow as tf
            print(f"✅ TensorFlow يعمل - الإصدار: {tf.__version__}")

            # اختبار متقدم
            test_model = tf.keras.Sequential([
                tf.keras.layers.Dense(1, input_shape=[1])
            ])
            print("✅ Keras يعمل بشكل صحيح")
            return True

        except Exception as e:
            print(f"❌ خطأ في اختبار TensorFlow: {e}")
            return False

    except Exception as e:
        print(f"❌ خطأ في إصلاح TensorFlow: {e}")
        return False

def create_tensorflow_free_config():
    """إنشاء إعداد بدون TensorFlow"""
    
    print("\n🔧 إنشاء إعداد بدون TensorFlow...")
    
    try:
        # إنشاء ملف إعداد
        config_content = """
# إعداد بدون TensorFlow
TENSORFLOW_ENABLED = False
USE_SAFE_ANALYZER_ONLY = True
"""
        
        with open("tensorflow_config.py", "w", encoding="utf-8") as f:
            f.write(config_content)
        
        print("✅ تم إنشاء ملف الإعداد")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء الإعداد: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    
    print("🚀 إصلاح مشكلة TensorFlow")
    print("=" * 50)
    
    # محاولة إصلاح TensorFlow
    tensorflow_fixed = fix_tensorflow_issue()
    
    if not tensorflow_fixed:
        print("\n⚠️ لم يتم إصلاح TensorFlow - إنشاء إعداد بديل...")
        create_tensorflow_free_config()
    
    print("\n" + "=" * 50)
    print("📋 ملخص الإصلاح:")
    
    if tensorflow_fixed:
        print("✅ تم إصلاح مشكلة TensorFlow بنجاح")
        print("🎉 يمكنك الآن تشغيل التطبيق بجميع الميزات")
    else:
        print("⚠️ لم يتم إصلاح TensorFlow")
        print("💡 سيعمل التطبيق بالمحلل الآمن (بدون TensorFlow)")
        print("🔧 الميزات المتاحة: YOLO + OpenCV + MediaPipe")
    
    print("\n🚀 لتشغيل التطبيق:")
    print("python main.py")
    
    return tensorflow_fixed

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف الإصلاح")
        sys.exit(0)
    except Exception as e:
        print(f"\n💥 خطأ غير متوقع: {e}")
        sys.exit(1)
