#!/usr/bin/env python3
"""
إصلاح مشكلة TensorFlow
TensorFlow Issue Fix

يقوم بإصلاح مشكلة توافق TensorFlow مع tf-keras
"""

import sys
import subprocess
import logging

logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')
logger = logging.getLogger(__name__)

def fix_tensorflow_issue():
    """إصلاح مشكلة TensorFlow"""
    
    print("🔧 إصلاح مشكلة TensorFlow...")
    print("=" * 50)
    
    try:
        # الخطوة 1: إلغاء تثبيت TensorFlow الحالي
        print("1️⃣ إلغاء تثبيت TensorFlow الحالي...")
        result = subprocess.run([
            sys.executable, "-m", "pip", "uninstall", "tensorflow", "-y"
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ تم إلغاء تثبيت TensorFlow")
        else:
            print("⚠️ لم يكن TensorFlow مثبتاً")
        
        # الخطوة 2: إلغاء تثبيت tf-keras إذا كان موجوداً
        print("\n2️⃣ إلغاء تثبيت tf-keras...")
        result = subprocess.run([
            sys.executable, "-m", "pip", "uninstall", "tf-keras", "-y"
        ], capture_output=True, text=True)
        
        # الخطوة 3: تثبيت TensorFlow مستقر
        print("\n3️⃣ تثبيت TensorFlow مستقر...")
        
        # محاولة تثبيت إصدارات مختلفة
        tensorflow_versions = [
            "tensorflow==2.15.0",
            "tensorflow==2.14.0", 
            "tensorflow==2.13.0",
            "tensorflow"  # أحدث إصدار مستقر
        ]
        
        tensorflow_installed = False
        
        for version in tensorflow_versions:
            print(f"   محاولة تثبيت {version}...")
            result = subprocess.run([
                sys.executable, "-m", "pip", "install", version
            ], capture_output=True, text=True)
            
            if result.returncode == 0:
                print(f"✅ تم تثبيت {version} بنجاح")
                tensorflow_installed = True
                break
            else:
                print(f"❌ فشل في تثبيت {version}")
        
        if not tensorflow_installed:
            print("⚠️ فشل في تثبيت TensorFlow - سيتم تشغيل التطبيق بدون TensorFlow")
            return False
        
        # الخطوة 4: اختبار TensorFlow
        print("\n4️⃣ اختبار TensorFlow...")
        try:
            import tensorflow as tf
            print(f"✅ TensorFlow يعمل - الإصدار: {tf.__version__}")
            return True
        except Exception as e:
            print(f"❌ خطأ في اختبار TensorFlow: {e}")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في إصلاح TensorFlow: {e}")
        return False

def create_tensorflow_free_config():
    """إنشاء إعداد بدون TensorFlow"""
    
    print("\n🔧 إنشاء إعداد بدون TensorFlow...")
    
    try:
        # إنشاء ملف إعداد
        config_content = """
# إعداد بدون TensorFlow
TENSORFLOW_ENABLED = False
USE_SAFE_ANALYZER_ONLY = True
"""
        
        with open("tensorflow_config.py", "w", encoding="utf-8") as f:
            f.write(config_content)
        
        print("✅ تم إنشاء ملف الإعداد")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء الإعداد: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    
    print("🚀 إصلاح مشكلة TensorFlow")
    print("=" * 50)
    
    # محاولة إصلاح TensorFlow
    tensorflow_fixed = fix_tensorflow_issue()
    
    if not tensorflow_fixed:
        print("\n⚠️ لم يتم إصلاح TensorFlow - إنشاء إعداد بديل...")
        create_tensorflow_free_config()
    
    print("\n" + "=" * 50)
    print("📋 ملخص الإصلاح:")
    
    if tensorflow_fixed:
        print("✅ تم إصلاح مشكلة TensorFlow بنجاح")
        print("🎉 يمكنك الآن تشغيل التطبيق بجميع الميزات")
    else:
        print("⚠️ لم يتم إصلاح TensorFlow")
        print("💡 سيعمل التطبيق بالمحلل الآمن (بدون TensorFlow)")
        print("🔧 الميزات المتاحة: YOLO + OpenCV + MediaPipe")
    
    print("\n🚀 لتشغيل التطبيق:")
    print("python main.py")
    
    return tensorflow_fixed

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف الإصلاح")
        sys.exit(0)
    except Exception as e:
        print(f"\n💥 خطأ غير متوقع: {e}")
        sys.exit(1)
