#!/usr/bin/env python3
"""
إصلاح سريع لمشاكل TensorFlow
Quick TensorFlow Fix

إصلاح سريع وفعال لأي مشاكل مستقبلية في TensorFlow
"""

import sys
import subprocess
import os

def setup_environment():
    """إعداد البيئة بسرعة"""
    print("🔧 إعداد البيئة...")
    
    # متغيرات البيئة الأساسية
    env_vars = {
        'TF_CPP_MIN_LOG_LEVEL': '3',
        'TF_ENABLE_ONEDNN_OPTS': '0',
        'PROTOCOL_BUFFERS_PYTHON_IMPLEMENTATION': 'python',
        'CUDA_VISIBLE_DEVICES': '-1',
        'PYTHONIOENCODING': 'utf-8'
    }
    
    for key, value in env_vars.items():
        os.environ[key] = value
    
    # تعطيل التحذيرات
    import warnings
    warnings.filterwarnings('ignore')
    
    print("✅ تم إعداد البيئة")

def quick_test():
    """اختبار سريع للنظام"""
    print("\n🔍 اختبار سريع...")
    
    try:
        import tensorflow as tf
        print(f"✅ TensorFlow {tf.__version__} يعمل")
        
        # اختبار بسيط
        test = tf.constant([1, 2, 3])
        print("✅ العمليات الأساسية تعمل")
        
        return True
    except Exception as e:
        print(f"❌ مشكلة في TensorFlow: {e}")
        return False

def quick_fix():
    """إصلاح سريع"""
    print("\n🔧 إصلاح سريع...")
    
    # قائمة المكتبات الأساسية للتحديث
    packages = [
        "tensorflow",
        "protobuf", 
        "google-generativeai",
        "google-ai-generativelanguage"
    ]
    
    for package in packages:
        print(f"   تحديث {package}...")
        try:
            result = subprocess.run([
                sys.executable, "-m", "pip", "install", "--upgrade", package
            ], capture_output=True, text=True, timeout=60)
            
            if result.returncode == 0:
                print(f"   ✅ تم تحديث {package}")
            else:
                print(f"   ⚠️ تحذير في {package}")
        except subprocess.TimeoutExpired:
            print(f"   ⏰ انتهت مهلة {package}")
        except Exception as e:
            print(f"   ❌ خطأ في {package}: {e}")
    
    print("✅ انتهى الإصلاح السريع")

def main():
    """الدالة الرئيسية"""
    print("⚡ إصلاح TensorFlow السريع")
    print("=" * 40)
    
    # إعداد البيئة
    setup_environment()
    
    # اختبار أولي
    if quick_test():
        print("\n🎉 TensorFlow يعمل بشكل جيد!")
        print("لا حاجة للإصلاح.")
        return True
    
    # إصلاح سريع
    quick_fix()
    
    # اختبار نهائي
    print("\n🔍 اختبار نهائي...")
    if quick_test():
        print("\n🎉 تم الإصلاح بنجاح!")
        print("يمكنك الآن تشغيل التطبيق:")
        print("python main.py")
        return True
    else:
        print("\n⚠️ يحتاج إصلاح شامل")
        print("شغل: python fix_tensorflow_issue.py")
        return False

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف الإصلاح")
        sys.exit(0)
    except Exception as e:
        print(f"\n💥 خطأ: {e}")
        sys.exit(1)
